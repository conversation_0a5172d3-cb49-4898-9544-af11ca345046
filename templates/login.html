{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900">Welcome Back</h1>
            <p class="mt-2 text-sm text-gray-600">
                Sign in to continue your learning journey
            </p>
        </div>

        <!-- Password Reset Success Message -->
        <div id="password-reset-success" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Success!</strong>
            <span class="block sm:inline">Your password has been reset. Please log in with your new password.</span>
        </div>

        <!-- Login Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01] duration-300">
            {% if otp_sent %}
                <!-- OTP Verification Form -->
                <div class="text-center space-y-4 mb-6">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100">
                        <i class="fas fa-key text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Verify Your Email</h3>
                    <p class="text-sm text-gray-600">
                        We've sent a verification code to <strong>{{ email }}</strong>.
                        Please enter the code to sign in.
                    </p>
                </div>

                <form method="POST" action="/login" class="space-y-6">
                    <input type="hidden" name="email" value="{{ email }}">

                    <div>
                        <label for="otp" class="block text-sm font-medium leading-6 text-gray-900">
                            Verification Code
                        </label>
                        <div class="mt-2 relative rounded-md shadow-sm">
                            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <input type="text" id="otp" name="otp" required maxlength="6"
                                   class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200 text-center text-lg tracking-widest"
                                   placeholder="000000"
                                   inputmode="numeric"
                                   pattern="[0-9]{6}">
                        </div>
                        <p class="mt-2 text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Enter the 6-digit code from your email.
                        </p>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-sign-in-alt text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            Verify & Sign In
                        </button>
                    </div>

                    <div class="text-center text-sm space-y-2">
                        <div>
                            <button type="button" id="resend-btn" onclick="resendCode()"
                                    class="text-indigo-600 hover:text-indigo-500 transition-colors duration-200 disabled:text-gray-400 disabled:cursor-not-allowed">
                                Resend verification code
                            </button>
                        </div>
                        <div>
                            <a href="{{ url_for('login') }}" class="text-gray-600 hover:text-indigo-600 transition-colors duration-200">
                                ← Back to email entry
                            </a>
                        </div>
                    </div>
                </form>
            {% else %}
                <!-- Email Entry Form -->
                <form method="POST" action="/login" class="space-y-6" id="login-form">
                    <div>
                        <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                            Email Address
                        </label>
                        <div class="mt-2 relative rounded-md shadow-sm">
                            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" required
                                   class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                                   placeholder="Enter your email address"
                                   {% if email %}value="{{ email }}"{% endif %}>
                        </div>
                        <p class="mt-2 text-xs text-gray-500">
                            <i class="fas fa-shield-alt mr-1"></i>
                            We'll send you a verification code to sign in.
                        </p>
                    </div>

                    <!-- Cooldown Timer Display -->
                    {% if cooldown_seconds %}
                    <div id="cooldown-display" class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-yellow-600 mr-2"></i>
                            <span class="text-sm text-yellow-800">
                                Please wait <span id="countdown-timer" class="font-semibold">{{ cooldown_seconds }}</span> seconds before requesting another code.
                            </span>
                        </div>
                    </div>
                    {% endif %}

                    <div>
                        <button type="submit" id="submit-btn"
                                class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                {% if cooldown_seconds %}disabled{% endif %}>
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-paper-plane text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            <span id="submit-text">Send Verification Code</span>
                        </button>
                    </div>

                    <div class="text-center text-sm">
                        <p class="text-gray-600">
                            Don't have an account?
                            <a href="{{ url_for('register') }}" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Create one now
                            </a>
                        </p>
                    </div>
                </form>
            {% endif %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if we have a reset=success parameter in the URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('reset') === 'success') {
            // Show the success message
            const successMessage = document.getElementById('password-reset-success');
            if (successMessage) {
                successMessage.classList.remove('hidden');
            }
        }

        // Handle cooldown timer
        {% if cooldown_seconds %}
        let timeLeft = {{ cooldown_seconds }};
        const countdownTimer = document.getElementById('countdown-timer');
        const submitBtn = document.getElementById('submit-btn');
        const submitText = document.getElementById('submit-text');
        const cooldownDisplay = document.getElementById('cooldown-display');

        function updateCountdown() {
            if (timeLeft > 0) {
                countdownTimer.textContent = timeLeft;
                timeLeft--;
                setTimeout(updateCountdown, 1000);
            } else {
                // Enable the button and hide cooldown display
                submitBtn.disabled = false;
                submitBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                submitText.textContent = 'Send Verification Code';
                if (cooldownDisplay) {
                    cooldownDisplay.style.display = 'none';
                }
            }
        }

        // Start the countdown
        updateCountdown();
        {% endif %}
    });

    // Resend code function for OTP verification
    function resendCode() {
        const resendBtn = document.getElementById('resend-btn');
        if (resendBtn) {
            resendBtn.disabled = true;
            resendBtn.textContent = 'Sending...';

            // Create a form to resend the code
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/login';

            const emailInput = document.createElement('input');
            emailInput.type = 'hidden';
            emailInput.name = 'email';
            emailInput.value = '{{ email }}';

            form.appendChild(emailInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}