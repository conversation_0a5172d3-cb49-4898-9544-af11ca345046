{% extends 'base.html' %}

{% block title %}{{ chapter.title }} - Chemistry Notes{% endblock %}

{% block head %}
{{ super() }}
<!-- Add required libraries for LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header with Back Button -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ url_for('notes') }}"
               class="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Back to Chapters
            </a>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">{{ chapter.title }}</h1>
        <p class="mt-2 text-lg text-gray-600">Chemistry lecture notes</p>
        <p class="text-sm text-gray-500 mt-1">{{ chapter.filename }}</p>
    </div>

    <!-- Main Content Layout with Sidebar -->
    <div class="flex gap-8">
        <!-- Table of Contents Sidebar -->
        {% if chapter.toc_sections %}
        <div class="hidden lg:block w-64 flex-shrink-0">
            <div class="sticky top-8">
                <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900 flex items-center">
                            <svg class="mr-2 h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                            </svg>
                            Table of Contents
                        </h3>
                    </div>
                    <div class="max-h-96 overflow-y-auto">
                        <nav class="p-2">
                            {% for section in chapter.toc_sections %}
                            <a href="#{{ section.id }}"
                               class="toc-link block px-3 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-md transition-colors duration-200"
                               data-section-id="{{ section.id }}"
                               style="padding-left: {{ (section.level - 1) * 0.75 + 0.75 }}rem;">
                                {{ section.title }}
                            </a>
                            {% endfor %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Main Content -->
        <div class="flex-1 min-w-0">
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                <div class="p-6">
                    <!-- Mobile TOC Toggle -->
                    {% if chapter.toc_sections %}
                    <div class="lg:hidden mb-6">
                        <button id="mobile-toc-toggle"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                            </svg>
                            Table of Contents
                        </button>
                        <div id="mobile-toc" class="hidden mt-4 bg-gray-50 rounded-lg p-4">
                            <nav>
                                {% for section in chapter.toc_sections %}
                                <a href="#{{ section.id }}"
                                   class="toc-link block px-3 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-white rounded-md transition-colors duration-200"
                                   data-section-id="{{ section.id }}"
                                   style="padding-left: {{ (section.level - 1) * 0.75 + 0.75 }}rem;">
                                    {{ section.title }}
                                </a>
                                {% endfor %}
                            </nav>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Markdown Content with LaTeX support -->
                    <div class="notes-content prose prose-lg max-w-none">
                        {{ chapter.html | safe }}
                    </div>
                </div>
            </div>

            <!-- Navigation Footer -->
            <div class="mt-8 flex justify-center">
                <a href="{{ url_for('notes') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Back to All Chapters
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Custom styles for notes content */
    .notes-content {
        line-height: 1.7;
        color: #374151;
    }

    .notes-content h1,
    .notes-content h2,
    .notes-content h3,
    .notes-content h4,
    .notes-content h5,
    .notes-content h6 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
        line-height: 1.25;
        color: #111827;
        position: relative;
        scroll-margin-top: 2rem;
    }

    /* Section link styling */
    .notes-content .section-link {
        opacity: 0;
        margin-left: 0.5rem;
        color: #6b7280;
        text-decoration: none;
        transition: opacity 0.2s ease;
    }

    .notes-content h1:hover .section-link,
    .notes-content h2:hover .section-link,
    .notes-content h3:hover .section-link,
    .notes-content h4:hover .section-link,
    .notes-content h5:hover .section-link,
    .notes-content h6:hover .section-link {
        opacity: 1;
    }

    .notes-content .section-link:hover {
        color: #3b82f6;
    }

    /* TOC active state */
    .toc-link.active {
        background-color: #dbeafe;
        color: #1d4ed8;
        font-weight: 500;
    }
    
    .notes-content h1 {
        font-size: 2rem;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    
    .notes-content h2 {
        font-size: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 0.25rem;
    }
    
    .notes-content h3 {
        font-size: 1.25rem;
    }
    
    .notes-content p {
        margin-bottom: 1rem;
    }
    
    .notes-content ul,
    .notes-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }
    
    .notes-content li {
        margin-bottom: 0.5rem;
    }
    
    .notes-content img {
        max-width: 100%;
        height: auto;
        margin: 1rem 0;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    
    /* Table styles - same as edit_question */
    .notes-content table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        background-color: white;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    
    .notes-content th,
    .notes-content td {
        padding: 0.75rem 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .notes-content th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .notes-content tr:hover {
        background-color: #f9fafb;
    }
    
    .notes-content tr:last-child td {
        border-bottom: none;
    }
    
    /* Code blocks */
    .notes-content pre {
        background-color: #f3f4f6;
        border-radius: 0.5rem;
        padding: 1rem;
        overflow-x: auto;
        margin: 1rem 0;
    }
    
    .notes-content code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
    
    .notes-content pre code {
        background-color: transparent;
        padding: 0;
    }
    
    /* LaTeX math styling */
    .notes-content .katex {
        font-size: 1.1em;
    }
    
    .notes-content .katex-display {
        margin: 1rem 0;
    }
    
    /* Blockquotes */
    .notes-content blockquote {
        border-left: 4px solid #3b82f6;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: #6b7280;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Process LaTeX equations in the notes content
    if (typeof katex !== 'undefined') {
        // Process display math
        const displayMathElements = document.querySelectorAll('.katex-display-math');
        displayMathElements.forEach(function(element) {
            const latex = element.getAttribute('data-latex');
            try {
                const rendered = katex.renderToString(latex, {
                    displayMode: true,
                    throwOnError: false
                });
                element.innerHTML = rendered;
                element.classList.remove('katex-display-math');
            } catch (error) {
                console.error('Error rendering display LaTeX:', error, latex);
                element.textContent = '$$ ' + latex + ' $$';
            }
        });

        // Process inline math
        const inlineMathElements = document.querySelectorAll('.katex-inline-math');
        inlineMathElements.forEach(function(element) {
            const latex = element.getAttribute('data-latex');
            try {
                const rendered = katex.renderToString(latex, {
                    displayMode: false,
                    throwOnError: false
                });
                element.innerHTML = rendered;
                element.classList.remove('katex-inline-math');
            } catch (error) {
                console.error('Error rendering inline LaTeX:', error, latex);
                element.textContent = '$ ' + latex + ' $';
            }
        });
    }

    // Mobile TOC toggle functionality
    const mobileToggle = document.getElementById('mobile-toc-toggle');
    const mobileToc = document.getElementById('mobile-toc');

    if (mobileToggle && mobileToc) {
        mobileToggle.addEventListener('click', function() {
            mobileToc.classList.toggle('hidden');
        });
    }

    // Smooth scrolling for TOC links
    const tocLinks = document.querySelectorAll('.toc-link');
    tocLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Hide mobile TOC after clicking
                if (mobileToc && !mobileToc.classList.contains('hidden')) {
                    mobileToc.classList.add('hidden');
                }
            }
        });
    });

    // Active section highlighting
    function updateActiveSection() {
        const sections = document.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]');
        const tocLinks = document.querySelectorAll('.toc-link');

        let activeSection = null;
        const scrollPosition = window.scrollY + 100; // Offset for better UX

        sections.forEach(function(section) {
            const sectionTop = section.offsetTop;
            if (scrollPosition >= sectionTop) {
                activeSection = section.id;
            }
        });

        // Update active state in TOC
        tocLinks.forEach(function(link) {
            const sectionId = link.getAttribute('data-section-id');
            if (sectionId === activeSection) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    // Throttled scroll handler for performance
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(updateActiveSection, 50);
    });

    // Initial active section update
    updateActiveSection();

    // Auto-scroll to target section if specified
    {% if chapter.target_section %}
    const targetSection = document.getElementById('{{ chapter.target_section }}');
    if (targetSection) {
        setTimeout(function() {
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 500); // Small delay to ensure page is fully loaded
    }
    {% endif %}
});
</script>

{% endblock %}
